"""
对话相关数据模型
包含对话会话、消息等相关模型
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Optional

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from .user import User
    from .topic import Topic


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ConversationStatus(str, Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


# Shared properties
class ConversationBase(SQLModel):
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    status: ConversationStatus = Field(default=ConversationStatus.ACTIVE)


# Properties to receive via API on creation
class ConversationCreate(ConversationBase):
    topic_id: uuid.UUID | None = None


# Properties to receive via API on update
class ConversationUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    status: ConversationStatus | None = None


# Database model, database table inferred from class name
class Conversation(ConversationBase, table=True):
    __tablename__ = "conversation"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    topic_id: uuid.UUID | None = Field(foreign_key="topic.id", nullable=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    owner: "User | None" = Relationship()
    topic: "Topic | None" = Relationship()
    messages: list["ConversationMessage"] = Relationship(back_populates="conversation", cascade_delete=True)


# Properties to return via API, id is always required
class ConversationPublic(ConversationBase):
    id: uuid.UUID
    owner_id: uuid.UUID
    topic_id: uuid.UUID | None
    created_at: datetime
    updated_at: datetime


class ConversationsPublic(SQLModel):
    data: list[ConversationPublic]
    count: int


# Conversation message models
class ConversationMessageBase(SQLModel):
    role: MessageRole
    content: str


class ConversationMessageCreate(ConversationMessageBase):
    conversation_id: uuid.UUID


class ConversationMessageUpdate(SQLModel):
    content: str | None = None


class ConversationMessage(ConversationMessageBase, table=True):
    __tablename__ = "conversation_message"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: uuid.UUID = Field(foreign_key="conversation.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    conversation: Conversation | None = Relationship(back_populates="messages")


class ConversationMessagePublic(ConversationMessageBase):
    id: uuid.UUID
    conversation_id: uuid.UUID
    created_at: datetime


class ConversationMessagesPublic(SQLModel):
    data: list[ConversationMessagePublic]
    count: int
