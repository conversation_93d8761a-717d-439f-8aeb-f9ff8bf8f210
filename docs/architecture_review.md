# 架构审查报告 — 面向远期功能需求的兼容性评估

版本: 1.0
日期: 2025-08-14
作者: Roo (架构审查)

摘要
本报告基于项目蓝图与架构文档（参见 [`docs/PROJECT_BLUEPRINT.md:1`] 和 [`docs/2_Architecture/01_overview.md:1`]）对当前技术选型与高层实现做出面向“远期功能需求”的兼容性评估。结论：总体选型合理，但若希望支持大规模长期记忆、多人并发、丰富多模态与语音功能，需在以下关键领域立即补强以避免未来不可扩展或难以维护的风险。

关键高风险项（摘要）
1. 搜索/向量层（Manticore）在单实例或未参数化的情况下存在可扩展性与成本风险。
2. 向量化（Embedding Service）尚未完备；需保证可插拔性、批量化与缓存策略。
3. 动态摘要与回溯映射需求要求在数据模型中保存精确的 source pointers。
4. 后台任务（Dramatiq）需队列分级、幂等性和速率控制，避免雪崩式调用外部服务。
5. Conversation 检索策略需要更强的 rerank/去重/时间衰减逻辑以保障长期上下文质量。
6. 数据库表若无分区策略将面临大规模数据增长的查询/维护瓶颈。
7. LLM 集成层需统一的限流、回退与成本策略以支撑多场景应用（实时与离线）。

逐项风险、替代方案与实施建议（短中长期）
1) 搜索与向量存储（Manticore）
- 风险：单实例内存与查询瓶颈；索引参数若硬编码难以调整。
- 短期：把 knn_dims、hnsw 参数、索引命名空间等项配置化，按 topic_id 支持分表或命名空间写入。
- 中期：设计分片/多实例查询聚合策略，监控内存/构建时长并制定资源基线。
- 长期：抽象搜索客户端层，支持切换/扩展到 Milvus/Weaviate/PGVector 等分布式引擎。

2) 向量化管道（Embedding Service）
- 风险：未实现可插拔/批量/缓存会导致高成本与高延迟。
- 短期：先实现统一 EmbeddingService 接口，支持 batch embed、本地缓存（Redis）、fallback。
- 中期：按场景选择模型（摘要/对话/检索）并实现在线 reindex 工具。
- 长期：支持模型版本管理与平滑向量迁移。

3) 动态摘要与回溯映射
- 风险：摘要无法精确回溯到 message/chunk。
- 短期：在 summary 表中新增 source pointer 字段（document_id, chunk_id, message_id, start_pos, end_pos）。
- 中期：在 Manticore 的 metadata 中索引这些字段，前端实现点击回溯。
- 长期：实现摘要版本控制与合并机制。

4) 后台任务（Dramatiq）
- 风险：任务无队列分级与速率管理易触发外部限额或系统拥堵。
- 短期：为 parse/embed/summarize 配置独立队列与并发限制；实现请求限流与指数退避。
- 中期：拆分 worker（embedding-worker、summary-worker），并结合监控与 autoscale。
- 长期：基于队列长度与 SLA 自动扩缩容。

5) Conversation 检索与上下文管理
- 风险：简单 top-k 检索在长会话下会拉出低质量上下文。
- 短期：实现 hybrid scoring（向量相似度+时间衰减+主题相关度）与去重/rerank。
- 中期：引入 Context Cache（会话级短期记忆）降低重复检索。
- 长期：建立用户知识状态 embedding，用于个性化检索与推荐。

6) 数据模型与扩展性（Postgres + SQLModel）
- 风险：单表增长导致查询/备份/迁移困难。
- 短期：对大表启用按时间或 topic 分区；增加必需索引（topic_id, conversation_id, created_at）。
- 中期：冷热分层存储（热表 Postgres、冷数据归档对象存储）。
- 长期：评估事件溯源或分析仓库方案支持大规模审计与行为分析。

7) LLM 集成与成本控制
- 风险：多模型并发调用导致成本不可控与响应不稳定。
- 短期：实现 LLM Gateway 层：限流、fallback、本地缓存与异步处理策略。
- 中期：任务级模型选择策略（对话用低延迟模型，批量摘要用更廉价模型）。
- 长期：混合云/自托管模型部署以降低长期成本。

优先级建议（立即 -> 中期 -> 长期）
- 立即（Sprint 0-2 周）:
  1. 配置化 Manticore/向量索引参数与分表策略。
  2. EmbeddingService 接口实现（batch, cache, fallback）。
  3. 在 summary 数据模型中加入 source pointer。
  4. Dramatiq 队列分级与基本速率限制。
- 中期（1-3 个月）:
  1. 植入检索 rerank 层与去重策略。
  2. Worker 拆分与资源隔离（embedding/summary）。
  3. Postgres 分区与冷热数据策略。
- 长期（3-12 个月）:
  1. 搜索引擎分片或迁移方案（支持多后端）。
  2. 用户知识状态建模与长期记忆策略。
  3. 多模态流水线与媒体处理平台集成。

可执行的短期任务清单（供直接落地）
- 在配置文件中暴露：VECTOR_DIMS, HNSW_M, HNSW_EF_CONSTRUCTION, INDEX_NAMESPACE_TEMPLATE。
- 在 EmbeddingService 中加入 batch_embed(texts[]) API，并在大文件导入中优先使用 batch。
- 为 summary 数据模型添加字段并在摘要生成 worker 中写入 pointers。
- 将 Dramatiq 的 tasks 分为 parse_queue, embed_queue, summary_queue，设置不同 concurrency。
- 增加一个 rerank/aggregate 层的伪接口，暂以简单权重融合实现（向量得分 * w1 + 时间衰减 * w2 + topic_match * w3）。

下一步交付物
- 我将把本审查写入仓库文件 [`docs/architecture_review.md`]（已生成）。
- 建议后续进入“代码审计”阶段：阅读并验证 `backend/app/manticore_client.py`, `backend/app/tasks.py`, `backend/app/dramatiq_config.py`, 以及 `services/embedding/` 的实现（如需我来做，请允许我切换到 code 模式并逐文件读取/审查）。

状态更新（TODO）
- 我已完成架构文档阅读与高级审查结论草稿，准备把该文件写入仓库（已完成）。
- 如果你同意，我将下一步迁移到代码级别审计（深入读取并列出具体修改点）。

最终交付
- 本文档（`docs/architecture_review.md`）为本次高级审查交付物，包含问题点、缓解方案与优先级行动项，适合直接转化为 Issue 或 Sprint 任务列表。
