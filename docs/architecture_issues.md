**说明**: 本文件列出的架构改进项应以最新模块化重构方案 [`docs/2_Architecture/03_modular_refactor_plan.md`](./2_Architecture/03_modular_refactor_plan.md:1) 为实施参考。项目实际实现已将核心引擎放置在 `engines/text_splitter/`，后端模型与 CRUD 已模块化为 `backend/app/models/` 与 `backend/app/crud/`，并通过相应的 `__init__.py` 重新导出以保证对现有 import 路径和 API 的向后兼容。请在执行每项改动时遵循“渐进式迁移、零停机、保持兼容”的原则。
  
# Architecture Issues — 优先级任务清单（短期优先、低成本改进）

说明：此清单基于架构审查报告（见 [`docs/architecture_review.md:1`])，聚焦“最低摩擦、最高价值”的短期改进，目的是在快速推进 MVP 的同时为未来扩展做好铺垫。每项包含问题描述、建议改动、优先级、预估工作量与负责人建议。

---

## 1. 配置化向量索引与参数
- 问题：向量索引参数（向量维度、HNSW 参数等）若硬编码，后续调整或迁移成本高。
- 建议改动：
  - 在全局配置中暴露：VECTOR_DIMS、HNSW_M、HNSW_EF_CONSTRUCTION、INDEX_NAMESPACE_TEMPLATE。
  - 在 embedding 写入/索引逻辑处读取这些配置（示例文件：[`backend/app/manticore_client.py:1`]）。
- 优先级：P0（高）
- 预估工作量：半天到 1 天
- 输出产物：新增/更新配置项 + README 更新（配置说明）
- 负责人建议：后端工程师（熟悉 manticore 客户端）

## 2. EmbeddingService: 实现 batch_embed + 本地缓存接口
- 问题：单次请求 embedding 在批量导入或并发场景下成本高且延迟大。
- 建议改动：
  - 在 Embedding 服务中添加 `batch_embed(texts: List[str]) -> List[vector]` 接口。
  - 引入简单的 Redis 缓存（短期：缓存最近生成的 text->vector 结果）。
  - 将 embedding 后端通过配置可切换（OpenAI/本地/fallback）。
  - 参考位置：[`services/embedding/README.md:1`]（模块占位）
- 优先级：P0（高）
- 预估工作量：2-3 人日
- 输出产物：API 接口 + 缓存中间件 + 文档
- 负责人建议：后端 + infra 配合（Redis）

## 3. 在 summary 数据模型中加入 source pointers
- 问题：动态摘要需要能够精确回溯到原始 message/document chunk，否则无法实现“点击摘要跳转到原文”功能。
- 建议改动：
  - 在 summary 表中增加字段：source_type, source_id (document_id / conversation_id), chunk_id, start_pos, end_pos, message_id。
  - 在摘要生成 worker（Dramatiq）中写入这些 pointers。
  - 参考实现位置：[`backend/app/tasks.py:1`]（摘要任务触发点）
- 优先级：P0（中高）
- 预估工作量：1-2 人日（含 DB migration）
- 输出产物：alembic migration + 修改 worker 写入逻辑 + 前端回溯接口
- 负责人建议：后端（DB/Worker） + 前端协调

## 4. Dramatiq: 队列分级与并发控制
- 问题：不同任务（parse/embed/summarize）混用同一队列会导致互相干扰，无法隔离资源。
- 建议改动：
  - 配置并启用多个队列：parse_queue, embed_queue, summary_queue。
  - 在 `dramatiq_config` 中为每个队列设置不同并发限制与 rate limit（使用 Dramatiq RateLimiters）。
  - 在部署脚本中启动针对性 workers（例如 `dramatiq -p worker --queues embed_queue --processes 2`）。
  - 参考实现位置：[`backend/app/dramatiq_config.py:1`]（查看当前配置）
- 优先级：P0（高）
- 预估工作量：1 人日
- 输出产物：配置变更 + 部署说明
- 负责人建议：后端 + SRE

## 5. Embedding 写入作业使用批量/幂等设计
- 问题：批量导入时重复写入或中断重试会导致重复计算或收费。
- 建议改动：
  - 对批量写入实现幂等 key（例如 based on document_id + chunk_hash），写入前检查是否已存在向量/已上报成功。
  - 批量任务应拆分为固定大小 chunk（例如 1000 文档/批）并逐批提交。
  - 在任务成功后写入已完成标记，避免重复。
- 优先级：P0（高）
- 预估工作量：1-2 人日
- 输出产物：任务改造 + 幂等检查逻辑
- 负责人建议：后端

## 6. 增加简单的 rerank 层（混合检索初版）
- 问题：直接使用 top-k 向量检索会导致长度会话中检索质量下降，需引入简单权重融合避免上下文污染。
- 建议改动：
  - 实现一个轻量的 rerank 函数：score = alpha * vector_score + beta * text_match_score + gamma * time_decay。
  - 在 ConversationService 调用检索后做一次 rerank，再返回给 LLM。
  - 首版使用固定权重并在监控中观察效果，后期可做自动调优。
  - 参考位置：[`services/conversation/`]（conversation 服务）
- 优先级：P1（中）
- 预估工作量：1 人日
- 输出产物：rerank module + 单元测试
- 负责人建议：后端算法工程师

## 7. Postgres：对大表进行分区或添加必要索引（短期方案）
- 问题：对话/摘要表随着用户增长会变大，影响查询性能。
- 建议改动：
  - 为 conversation/summary table 增加索引：topic_id, conversation_id, created_at。
  - 若预期很高的数据量，先启用基于时间（created_at）或 topic 的分区策略。
  - 参考位置：[`docs/2_Architecture/02_data_model_v1.md:1`]
- 优先级：P1（中）
- 预估工作量：0.5-1 人日（索引）；分区视情况 1-2 人日
- 输出产物：migration scripts + 文档
- 负责人建议：后端 DB 负责人

## 8. 监控与报警（Dramatiq + Manticore）
- 问题：没有监控/报警将难以及时发现索引构建/队列堆积/外部调用失败等问题。
- 建议改动：
  - 启用 Dramatiq Prometheus 中间件并在 Grafana 中配置基础仪表板（队列长度、失败率、处理时延 p95/p99）。
  - 配置 Manticore 的 `SHOW STATUS` 导出指标（或通过自定义脚本采集）并纳入监控。
  - 为关键任务设置 Sentry 报警（通过自定义中间件捕获异常）。
- 优先级：P1（中）
- 预估工作量：1-2 人日
- 输出产物：监控配置 + dashboard 模板 + 告警规则
- 负责人建议：SRE/后端

---

附注：每项改动均尽量以“最小可行改动（low-friction）”为目标，以便快速在 MVP 中落地，同时为未来的分片/横向扩展留出接口和迁移路径。

后续建议动作（选项）
- 我可以将上述每一项拆成 Github Issue 草稿并写入 `docs/architecture_issues.md`（已完成），或我可以直接开始按优先级逐项在代码中定位需要改动的文件并提交 PR 草案（需要你允许我进入 code 模式逐文件读取）。请选择下一步操作。
