# 架构概览

本文档提供了“知深学习导师”项目的高层架构视图，旨在帮助开发者快速理解系统的核心组件和它们之间的交互关系。

## 核心设计思想

项目采用**分层、模块化**的设计理念，将复杂的系统拆分为一系列高内聚、低耦合的服务和引擎。

- **前端层 (Frontend Layer)**: 用户交互的入口，负责展示界面和用户输入。
- **API网关层 (API Gateway Layer)**: 作为所有外部请求的统一入口，负责路由、认证和请求聚合。
- **应用服务层 (Application Services)**: 实现核心业务逻辑，如用户管理、主题会话、文档处理等。
- **核心引擎层 (Core Engine Layer)**: 提供底层的、可复用的核心能力，如语义化文本分割、向量化和智能上下文检索。
- **数据持久层 (Data Persistence Layer)**: 负责所有数据的存储和管理。

## 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend Layer                    │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │   Web UI    │ │  WebSocket  │                           │
│  │   (React)   │ │   Client    │                           │
│  └─────────────┘ └─────────────┘                           │
├─────────────────────────────────────────────────────────────┤
│                  API网关层 API Gateway Layer                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │     BFF Gateway (Traefik + FastAPI)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  应用服务层 Application Services             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   User      │ │   Topic     │ │ Conversation│          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Summary   │ │     LLM     │          │
│  │  Service    │ │  Service    │ │ Integration │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  核心引擎层 Core Engine Layer                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Text-Splitter│ │ Embedding   │ │ Context     │          │
│  │   Engine    │ │  Service    │ │  Engine     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  数据持久层 Data Persistence Layer          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Manticore   │ │ PostgreSQL  │ │   Redis     │          │
│  │   Search    │ │  (关系数据)  │ │  (缓存)     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 关键组件说明

- **Web UI (React)**: 基于 React 的单页应用，提供丰富的用户交互体验。
- **BFF Gateway (FastAPI)**: 面向前端的网关，聚合后端服务，简化前端调用。
- **Application Services**: 一系列独立的微服务，每个服务负责一块具体的业务领域。
- **Text-Splitter Engine**: 核心的文本处理引擎，负责将原始文档智能地分割成语义完整的块。
- **Manticore Search**: 项目的“记忆核心”，利用其混合搜索能力（全文+向量）实现长期记忆和上下文感知。
- **PostgreSQL**: 存储核心的关系型数据，如用户信息、主题等。

## 进一步阅读

- **[数据模型](./02_data_model_v1.md)**: 查看初代 MVP 版本设计的核心数据表结构。
- **[模块化重构计划](./03_modular_refactor_plan.md)**: 了解从当前单体结构向未来微服务架构演进的详细规划。