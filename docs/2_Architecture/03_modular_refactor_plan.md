# 知深学习导师 - 模块化架构重构方案

## 📋 项目概述

**项目名称**: 知深学习导师 (Master-Know)  
**核心目标**: 个人化AI学习伴行系统，通过可追溯、可复盘的引导式对话，帮助用户真正内化知识  
**技术特色**: 基于 Manticore Search 的智能上下文引擎 + 语义化文档分割

## 🏗️ 模块化架构设计

### 核心原则
1. **单一职责**: 每个模块专注一个核心功能
2. **接口优先**: 模块间通过明确的API接口通信
3. **可独立部署**: 每个模块可以独立开发、测试、部署
4. **渐进式重构**: 基于现有 FastAPI 模板逐步模块化

### 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend Layer                    │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │   Web UI    │ │  WebSocket  │                           │
│  │   (React)   │ │   Client    │                           │
│  └─────────────┘ └─────────────┘                           │
├─────────────────────────────────────────────────────────────┤
│                  API网关层 API Gateway Layer                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │     BFF Gateway (Traefik + FastAPI)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  应用服务层 Application Services             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   User      │ │   Topic     │ │ Conversation│          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Summary   │ │     LLM     │          │
│  │  Service    │ │  Service    │ │ Integration │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  核心引擎层 Core Engine Layer                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Text-Splitter│ │ Embedding   │ │ Context     │          │
│  │   Engine    │ │  Service    │ │  Engine     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  数据持久层 Data Persistence Layer          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Manticore   │ │ PostgreSQL  │ │   Redis     │          │
│  │   Search    │ │  (关系数据)  │ │  (缓存)     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 模块目录结构重构

### 新的项目结构
```
master-know/
├── services/                      # 微服务模块
│   ├── gateway/                   # 🚪 API网关服务
│   ├── user/                      # 👤 用户服务
│   ├── topic/                     # 📚 主题服务
│   ├── document/                  # 📄 文档服务
│   ├── embedding/                 # 🔢 向量化服务
│   ├── context/                   # 🧠 上下文引擎
│   ├── llm/                       # 🤖 LLM集成服务
│   ├── conversation/              # 💬 对话服务
│   └── summary/                   # 📝 摘要服务
├── shared/                        # 共享组件
│   ├── models/                    # 数据模型
│   ├── utils/                     # 工具函数
│   ├── config/                    # 配置管理
│   └── exceptions/                # 异常定义
├── engines/                       # 核心引擎
│   ├── text_splitter/             # 🔪 文档分割引擎
│   └── search/                    # 🔍 搜索引擎
├── frontend/                      # 前端应用
├── deployment/                    # 部署配置
├── tests/                         # 测试套件
└── docs/                          # 项目文档
```

## 🎯 模块详细设计

### 1. Text-Splitter Engine (核心引擎)
**位置**: `engines/text_splitter/`
**职责**: 语义化文档分割
**技术栈**: semantic-text-splitter (Rust + Python bindings)

**核心功能**:
- 支持多种文档格式 (Text, Markdown, Code)
- 语义感知的智能分割
- 多种分割策略 (Token-based, Character-based, Range-based)
- 批量处理能力

**API接口**:
```python
class TextSplitterEngine:
    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]
    def split_markdown(self, content: str, max_tokens: int) -> List[TextChunk]
    def split_code(self, code: str, language: str) -> List[TextChunk]
    def batch_split(self, documents: List[Document]) -> List[List[TextChunk]]
```

### 2. Document Service (应用服务)
**位置**: `services/document/`
**职责**: 文档管理和处理
**依赖**: Text-Splitter Engine, Embedding Service

**核心功能**:
- 文档上传和存储
- 调用 Text-Splitter Engine 进行分割
- 文档元数据管理
- 与主题服务集成

### 3. Embedding Service (核心引擎)
**位置**: `services/embedding/`
**职责**: 文本向量化
**技术栈**: HuggingFace Transformers / OpenAI API

**核心功能**:
- 文本向量化
- 批量处理
- 多模型支持
- 向量缓存

### 4. Context Engine (核心引擎)
**位置**: `services/context/`
**职责**: 智能上下文检索
**依赖**: Manticore Search

**核心功能**:
- 混合搜索 (全文 + 向量)
- 上下文排序和过滤
- 历史对话检索
- 主题相关性计算

## 🚀 实施计划

### Phase 1: 核心引擎重构 (Week 1)
1. **Text-Splitter Engine 独立化**
   - 从现有 document_parser.py 重构
   - 增强语义分割能力
   - 添加批量处理支持

2. **Embedding Service 创建**
   - 独立的向量化服务
   - 支持多种模型
   - 与 Manticore 集成

3. **Context Engine 优化**
   - 基于现有 Manticore 配置
   - 增强检索算法
   - 添加缓存机制

### Phase 2: 应用服务重构 (Week 2)
4. **Document Service 重构**
   - 集成新的 Text-Splitter Engine
   - 异步处理流水线
   - 文档状态管理

5. **Topic Service 完善**
   - 主题管理功能
   - 文档关联
   - 权限控制

6. **User Service 简化**
   - 基础用户管理
   - 会话管理
   - 权限验证

### Phase 3: 高级功能 (Week 3-4)
7. **LLM Integration 优化**
8. **Conversation Service 重构**
9. **Summary Service 增强**
10. **Frontend 适配**

## 📊 技术决策

### Text-Splitter 集成策略
1. **保持现有集成**: 继续使用 semantic-text-splitter
2. **增强功能**: 添加更多分割策略和优化
3. **性能优化**: 批量处理和缓存机制
4. **监控**: 添加性能指标和错误追踪

### 服务通信
- **同步**: HTTP/REST API
- **异步**: Redis Pub/Sub + Dramatiq
- **实时**: WebSocket

### 数据一致性
- **最终一致性**: 异步处理场景
- **强一致性**: 关键业务操作
- **事务管理**: 跨服务事务处理

## 🔧 开发工具和规范

### 代码规范
- Python: PEP 8 + Type Hints
- API: OpenAPI 3.0 规范
- 测试: pytest + 覆盖率 > 80%
- 文档: 自动生成 + 手动维护

### 部署策略
- 容器化: Docker + Docker Compose
- 服务发现: Traefik
- 监控: Prometheus + Grafana
- 日志: 结构化日志 + ELK Stack

## 🛠️ 具体实施步骤

### Step 1: Text-Splitter Engine 重构
```bash
# 创建独立的文本分割引擎
mkdir -p engines/text_splitter
cd engines/text_splitter

# 核心文件结构
├── __init__.py
├── engine.py              # 主引擎类
├── strategies.py          # 分割策略
├── models.py             # 数据模型
├── config.py             # 配置管理
└── tests/                # 测试文件
```

**核心代码框架**:
```python
# engines/text_splitter/engine.py
from semantic_text_splitter import TextSplitter, MarkdownSplitter
from typing import List, Dict, Any
from .strategies import SplitStrategy
from .models import TextChunk, Document

class TextSplitterEngine:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self._splitters = {}

    def get_splitter(self, strategy: SplitStrategy) -> TextSplitter:
        """获取或创建分割器实例"""
        pass

    def split_text(self, text: str, strategy: SplitStrategy) -> List[TextChunk]:
        """文本分割主方法"""
        pass

    def batch_split(self, documents: List[Document]) -> Dict[str, List[TextChunk]]:
        """批量处理文档"""
        pass
```

### Step 2: 服务模块创建脚本
```bash
# 创建服务生成脚本
cat > scripts/create_service.py << 'EOF'
#!/usr/bin/env python3
"""
服务模块创建脚本
用法: python scripts/create_service.py <service_name>
"""
import os
import sys
from pathlib import Path

def create_service(service_name: str):
    service_dir = Path(f"services/{service_name}")
    service_dir.mkdir(parents=True, exist_ok=True)

    # 创建基础文件结构
    files = {
        "__init__.py": "",
        "main.py": f"# {service_name.title()} Service Main",
        "api.py": f"# {service_name.title()} Service API",
        "models.py": f"# {service_name.title()} Service Models",
        "config.py": f"# {service_name.title()} Service Config",
        "Dockerfile": f"# {service_name.title()} Service Dockerfile",
        "requirements.txt": "# Service specific requirements",
        "tests/__init__.py": "",
        "tests/test_api.py": f"# {service_name.title()} API Tests"
    }

    for file_path, content in files.items():
        full_path = service_dir / file_path
        full_path.parent.mkdir(exist_ok=True)
        full_path.write_text(content)

    print(f"✅ Service '{service_name}' created at {service_dir}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python scripts/create_service.py <service_name>")
        sys.exit(1)
    create_service(sys.argv[1])
EOF
```

### Step 3: 共享组件设计
```python
# shared/models/base.py
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class BaseEntity(BaseModel):
    id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class TextChunk(BaseEntity):
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    token_count: Optional[int] = None
    embedding: Optional[List[float]] = None

class Document(BaseEntity):
    title: str
    content: str
    file_type: str
    size: int
    chunks: Optional[List[TextChunk]] = None
```

## 🧪 测试策略

### 单元测试
```python
# engines/text_splitter/tests/test_engine.py
import pytest
from engines.text_splitter import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy

class TestTextSplitterEngine:
    def test_split_text_basic(self):
        engine = TextSplitterEngine({})
        strategy = TokenBasedStrategy(max_tokens=100)

        text = "这是一个测试文档。" * 50
        chunks = engine.split_text(text, strategy)

        assert len(chunks) > 1
        assert all(chunk.token_count <= 100 for chunk in chunks)

    def test_batch_processing(self):
        # 批量处理测试
        pass
```

### 集成测试
```python
# tests/integration/test_document_processing.py
import pytest
from services.document.api import DocumentService
from engines.text_splitter import TextSplitterEngine

class TestDocumentProcessingFlow:
    def test_full_document_processing_pipeline(self):
        # 测试完整的文档处理流程
        pass
```

## 📈 监控和可观测性

### 性能指标
```python
# shared/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# Text-Splitter 相关指标
text_split_requests = Counter('text_split_requests_total', 'Total text split requests')
text_split_duration = Histogram('text_split_duration_seconds', 'Text split duration')
chunk_count = Gauge('text_chunks_generated', 'Number of chunks generated')

# 文档处理指标
document_processing_requests = Counter('document_processing_requests_total', 'Total document processing requests')
document_processing_duration = Histogram('document_processing_duration_seconds', 'Document processing duration')
```

### 健康检查
```python
# shared/health/checker.py
from typing import Dict, Any
import asyncio

class HealthChecker:
    async def check_text_splitter_engine(self) -> Dict[str, Any]:
        try:
            # 测试 text-splitter 引擎
            return {"status": "healthy", "latency_ms": 10}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def check_all_services(self) -> Dict[str, Any]:
        checks = await asyncio.gather(
            self.check_text_splitter_engine(),
            # 其他服务检查...
        )
        return {"services": checks}
```

## 🔄 迁移策略

### 从现有代码迁移
1. **保持向后兼容**: 现有 API 继续工作
2. **渐进式替换**: 逐步替换内部实现
3. **功能标志**: 使用特性开关控制新旧功能
4. **数据迁移**: 平滑迁移现有数据

### 迁移检查清单
- [ ] Text-Splitter Engine 独立化完成
- [ ] 现有 document_parser.py 功能迁移
- [ ] API 兼容性测试通过
- [ ] 性能基准测试通过
- [ ] 文档更新完成

---

**文档版本**: 2.0.0
**创建日期**: 2025-08-14
**维护者**: AI Assistant
